"""Module for fetching PMC article XML data"""

import requests
from typing import Optional
from loguru import logger


def get_pmc_article_xml(pmcid: Optional[str] = None) -> Optional[str]:
    """Get the PMC article XML from the PMC ID and return it as a string

    Args:
        pmcid (str): The PMC ID of the article

    Returns:
        Optional[str]: The XML data of the article as a string, or None if failed

    Raises:
        ValueError: If PMCID is empty
    """
    # check if the pmcid is valid
    if pmcid in ["", None]:
        logger.error("PMCID is empty")
        raise ValueError("PMCID is empty")

    pmcid = str(pmcid).strip()
    if not pmcid.startswith("PMC"):
        pmcid = "PMC" + pmcid

    params = {
        "db": "pmc",
        "id": f"[{pmcid}]",
        "rettype": "xml",
    }

    try:
        logger.info(f"Fetching article XML for PMCID: {pmcid}")
        # Send the request to the NCBI
        response = requests.get(
            "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi",
            params=params,
            timeout=15,
        )
        response.raise_for_status()
        xml_data = response.text
        
        if not xml_data or len(xml_data.strip()) == 0:
            logger.warning(f"Empty XML response received for PMCID: {pmcid}")
            return None
            
        logger.debug(f"Successfully retrieved XML data for PMCID: {pmcid}")
        return xml_data

    except requests.exceptions.Timeout:
        logger.error(f"Request timed out for PMCID: {pmcid}")
        return None
    except requests.exceptions.HTTPError as e:
        logger.error(f"HTTP error occurred for PMCID {pmcid}: {str(e)}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed for PMCID {pmcid}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error occurred for PMCID {pmcid}: {str(e)}")
        return None
