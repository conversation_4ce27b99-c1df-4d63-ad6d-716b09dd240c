""" This script is used to search in pubmed and get the related article PMCIDs"""

import requests


class PMCSearch:
    """Class to search in PMC and get the related article PMCIDs"""

    def __init__(self):
        pass

    @staticmethod
    def search(query_text, parameters=None):
        """Search in PMC using query_text and
        return the related article PMCIDs as a list of strings

        Parameters
        ----------
        query_text : str
            The query text to search in pubmed
        parameters : dict, optional
            Additional parameters to add to the query, by default None
            some of the parameters are:
                - retmax: The number of results you want to get for each query
        """

        if query_text is None or query_text == "":
            raise ValueError("Query text cannot be empty")

        query_text = str(query_text).strip()

        # Set the parameters for the query
        params = {
            "db": "pmc",
            "term": f"[{query_text}]",
            "retmax": 100,  # Set the number of results per query you want here
            "usehistory": "y",
            "email": "[your email here]",
            "tool": "[name of your tool or script]",
            "retmode": "json",
            "sort": "relevance",
        }

        # Add any additional parameters to the query
        if parameters is not None:
            params.update(parameters)

        # Send the request to the NCBI
        response = requests.get(
            "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi",
            params=params,
            timeout=20,
        )

        # Check that we got a valid response
        if response.status_code != 200:
            raise ValueError(
                f"Query failed to run by returning code of {response.status_code}. {params}"
            )

        # Parse the initial response
        json_response = response.json()
        pmcids = json_response["esearchresult"].get("idlist", [])
        retstart = int(json_response["esearchresult"].get("retstart", 0))
        retmax = int(json_response["esearchresult"].get("retmax", 0))
        webenv = json_response["esearchresult"].get("webenv", None)
        querykey = json_response["esearchresult"].get("querykey", None)
        total_result_count = int(json_response["esearchresult"].get("count", 0))

        # Get the rest of the results in batches of retmax if there are any more

        while int(retstart) + int(retmax) < (total_result_count):
            params["retstart"] = int(retstart) + int(retmax)
            params["webenv"] = webenv
            params["query_key"] = querykey
            response = requests.get(
                "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi",
                params=params,
                timeout=20,
            )
            json_response = response.json()

            if response.status_code != 200:
                raise ValueError(
                    f"Query failed to run by returning code of {response.status_code}. {params}"
                )

            pmcids.extend(json_response["esearchresult"].get("idlist", []))
            retstart = int(json_response["esearchresult"]["retstart"])
            retmax = int(json_response["esearchresult"]["retmax"])

        return pmcids
