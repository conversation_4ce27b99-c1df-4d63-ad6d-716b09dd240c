"""Metadata handling utilities for PMC parser."""

from typing import Dict, Any, Optional

from lxml import etree
from loguru import logger
from src.api.citation_count import get_article_citation_count as get_count
from src.api.journal_ranking import get_journal_ranking as get_ranking


def extract_metadata(xml_tree: etree._Element) -> Dict[str, Any]:
    """Extract metadata from XML tree."""
    metadata: Dict[str, Any] = {}
    xml_tree = xml_tree.find(".//front")
    if xml_tree is None:
        return metadata

    # Article title
    title = xml_tree.find(".//article-title")
    metadata["title"] = title.text if title is not None else ""

    # Keywords
    keywords = [
        p.text.strip() for p in xml_tree.findall(".//kwd") if p.text is not None
    ]
    metadata["kwd"] = ", ".join(keywords).strip()

    # Authors
    metadata["authors"] = [
        {
            "first": (
                p.find("given-names").text if p.find("given-names") is not None else ""
            ),
            "middle": "",
            "last": p.find("surname").text if p.find("surname") is not None else "",
            "suffix": "",
            "affiliation": {},
            "email": "",
        }
        for p in xml_tree.findall(".//contrib/name")
    ]

    # Article IDs
    article_ids = []
    for article_id in xml_tree.findall(".//article-id"):
        article_ids.append(
            {
                "idtype": article_id.attrib.get("pub-id-type", ""),
                "value": article_id.text,
            }
        )
    metadata["articleids"] = article_ids

    # PMC ID
    for article_id in article_ids:
        if article_id["idtype"] == "pmc":
            value = article_id["value"]
            metadata["pmcid"] = f"PMC{value}" if not value.startswith("PMC") else value
            metadata["uid"] = value[3:] if value.startswith("PMC") else value
            break

    # Journal info
    journal_name = xml_tree.find(".//journal-title")
    metadata["fulljournalname"] = journal_name.text if journal_name is not None else ""

    abbrev = xml_tree.find(".//journal-id[@journal-id-type='iso-abbrev']")
    metadata["source"] = abbrev.text if abbrev is not None else ""

    # ISSN
    issn = xml_tree.find(".//issn")
    if issn is not None:
        for item in issn:
            if item.tag == "pub-type":
                metadata[f"{item.text}_issn"] = issn.text or ""

    # Publication dates
    month_map = {
        1: "Jan",
        2: "Feb",
        3: "Mar",
        4: "Apr",
        5: "May",
        6: "Jun",
        7: "Jul",
        8: "Aug",
        9: "Sep",
        10: "Oct",
        11: "Nov",
        12: "Dec",
        0: "",
    }

    for date in xml_tree.findall(".//pub-date"):
        date_type = date.attrib.get("pub-type", "")
        year = date.find("year")
        year_text = year.text if year is not None else ""

        month = date.find("month")
        month_num = int(month.text) if month is not None and month.text.isdigit() else 0
        month_text = month_map.get(month_num, "")

        day = date.find("day")
        day_text = day.text if day is not None else ""

        metadata[f"{date_type}date"] = f"{year_text} {month_text} {day_text}".strip()

    # Volume and issue
    for field in ["volume", "issue"]:
        elem = xml_tree.find(f".//{field}")
        metadata[field] = elem.text if elem is not None else ""

    # Pages
    fpages = xml_tree.find(".//fpage")
    lpages = xml_tree.find(".//lpage")
    fpages_text = fpages.text if fpages is not None else ""
    lpages_text = lpages.text if lpages is not None else ""
    metadata["pages"] = f"{fpages_text}-{lpages_text}".strip("-")

    return metadata


def get_article_citation_count(
    doi: Optional[str], pmcid: Optional[str] = None
) -> Optional[int]:
    """Get citation count from CrossRef API."""
    if doi is None:
        logger.warning(f"No DOI found for article {pmcid}")
        return None

    try:
        return get_count(doi, pmcid)
    except Exception as e:
        logger.error(f"Error getting citation count for {doi}: {e}")
        return None


def get_journal_ranking(
    journal_name: Optional[str], pmcid: Optional[str] = None
) -> Dict[str, Any]:
    """Get journal ranking information."""
    if journal_name is None:
        logger.warning(f"No journal name found for article {pmcid}")
        return {}

    try:
        return get_ranking(journal_name, pmcid)
    except Exception as e:
        logger.error(f"Error getting journal ranking for {journal_name}: {e}")
        return {}
