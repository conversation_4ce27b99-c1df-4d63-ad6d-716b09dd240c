"""XML handling utilities for PMC parser."""

from typing import Dict, List, Optional
from itertools import chain

from lxml.etree import ElementTree  # type: ignore
from loguru import logger

from .text_processors import clean_text  # Import here to avoid circular dependency


def remove_namespace(tree: ElementTree) -> None:
    """Strip namespace from parsed XML."""
    for node in tree.iter():
        try:
            has_namespace = node.tag.startswith("{")
        except AttributeError:
            continue  # node.tag is not a string (node is a comment or similar)
        if has_namespace:
            node.tag = node.tag.split("}", 1)[1]


def stringify_children(node: ElementTree) -> str:
    """
    Filters and removes possible Nones in texts and tails.
    ref: http://stackoverflow.com/questions/4624062/get-all-text-inside-a-tag-in-lxml
    """
    parts = (
        [node.text]
        + list(chain(*([c.text, c.tail] for c in node.getchildren())))
        + [node.tail]
    )
    return "".join(filter(None, parts)).strip()


def _extract_section_data(paragraphs, section_type: str) -> Optional[Dict[str, str]]:
    """Extract section data for a specific section type, like conclusion or discussion."""
    section_text = ""
    section_title = ""

    for paragraph in paragraphs:
        parent = paragraph.getparent()
        if not parent:
            continue

        parent_sec_type = parent.attrib.get("sec-type", "").lower()[:10]
        if parent_sec_type != section_type:
            continue

        text = stringify_children(paragraph)
        if not text:
            continue

        section_text += "\n" + text
        if not section_title:
            title = parent.find("title")
            if title is not None:
                section_title = stringify_children(title)

    if not section_text:
        return None

    return {
        "text": section_text.strip(),
        "section_title": section_title.strip(),
        "section_type": section_type.upper(),
    }


def extract_sections(
    xml_tree: ElementTree,
) -> Dict[str, List[Optional[Dict[str, str]]]]:
    """Extract sections (abstract, body text, etc.) from XML tree."""
    output_dict: Dict[str, List[Optional[Dict[str, str]]]] = {}

    # Extract abstract
    abstract = xml_tree.findall(".//abstract//p")
    text = ""
    for part in abstract:
        abstract_p = ["".join(p.itertext()) for p in part.iter("p")]
        text += "\n" + " ".join(abstract_p)
    text = clean_text(text)

    if text.strip() == "":
        output_dict["abstract"] = [None]
    else:
        output_dict["abstract"] = [
            {
                "text": text,
                "section_title": "Abstract",
                "section_type": "ABSTRACT",
            }
        ]

    # Extract body text sections
    paragraphs = xml_tree.xpath("//body//sec/p")
    body_text = []
    paragraph_id = 0

    if paragraphs:
        for paragraph in paragraphs:
            try:
                paragraph_text = stringify_children(paragraph)
                paragraph_text = clean_text(paragraph_text)

                paragraph_title = paragraph.find("../title")
                if paragraph_title is not None:
                    paragraph_title = stringify_children(paragraph_title)
                else:
                    paragraph_title = ""

                section_type = paragraph.getparent().attrib.get("sec-type", "")
                if not section_type:
                    section_type = (
                        paragraph.getparent().getparent().attrib.get("sec-type", "")
                    )

                if (
                    section_type.lower()
                    not in ["discussion", "discussions", "conclusion", "conclusions"]
                    and not any(
                        word in paragraph_title.lower()
                        for word in ["discussion", "conclusion"]
                    )
                    and paragraph_text
                ):
                    body_text.append(
                        {
                            "section_title": paragraph_title.strip(),
                            "text": paragraph_text,
                            "section_type": section_type.strip(),
                            "paragraph_id": paragraph_id,
                        }
                    )
                    paragraph_id += 1
            except Exception as e:
                logger.error(f"Error processing paragraph: {e}")
                continue

    output_dict["body_text"] = body_text if body_text else [None]

    # Extract conclusion and discussion sections
    for section_type in ["conclusion", "discussion"]:
        section_data = _extract_section_data(paragraphs, section_type)
        output_dict[section_type] = [section_data] if section_data else [None]

    return output_dict
