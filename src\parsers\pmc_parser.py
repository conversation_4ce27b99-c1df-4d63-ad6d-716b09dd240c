"""PMC article parser for extracting structured content from PMC XML articles."""

from typing import Dict, Optional, Any, Union, List
import concurrent.futures
from tqdm import tqdm

from lxml import etree
from loguru import logger

from src.api.pmc_article_fetcher import get_pmc_article_xml
from src.utils.xml_utils import clean_xml

from .xml_handlers import extract_sections
from .text_processors import (
    create_sentence_list,
    split_article_paragraphs,
    update_splits,
    clean_text,
    get_sentences,
)
from .metadata_handlers import (
    extract_metadata,
    get_article_citation_count,
    get_journal_ranking,
)


class PMCParser:
    """Class for generating a JSON object for a PMC article.

    The JSON object contains the abstract, conclusion, discussion, meta_data,
    body_text of the article as well as the journal ranking info and article
    split list. All sections include section title, section type, and text.

    Args:
        max_split_token_length (int): Maximum tokens in a split. Default: 500
        min_split_token_length (int): Minimum tokens in a split. Default: 100
        token_overlap (int): tokens to overlap between splits. Default: 10

    Attributes:
        json_output (Dict[str, Any]): The JSON object for the article
        max_split_token_length (int): Maximum tokens in a split
        min_split_token_length (int): Minimum tokens in a split
        token_overlap (int): tokens to overlap between splits
        pmcid (str): The PMCID of the current article
    """

    def __init__(
        self,
        max_split_token_length: int = 500,
        min_split_token_length: int = 100,
        token_overlap: int = 10,
    ) -> None:
        self.max_split_token_length = max_split_token_length
        self.token_overlap = token_overlap
        self.min_split_token_length = min_split_token_length
        self.json_output: Optional[Dict[str, Any]] = None
        self.pmcid: Optional[str] = None

    def build_pmc_json(
        self, xml_data: Union[str, etree._Element]
    ) -> Optional[Dict[str, Any]]:
        """Build JSON representation from XML data."""
        try:
            # Clean and parse XML
            xml_tree = clean_xml(xml_data)
            logger.debug("Successfully cleaned XML data")

            # Extract sections
            self.json_output = extract_sections(xml_tree)
            logger.debug("Successfully extracted sections")

            # Extract metadata
            self.json_output["meta_info"] = extract_metadata(xml_tree)
            logger.debug("Successfully extracted metadata")

            # Get journal name and DOI
            journal_name = self.json_output["meta_info"].get("fulljournalname")
            if journal_name is None:
                journal_name = self.json_output["meta_info"].get("source")

            doi = None
            for id_part in self.json_output["meta_info"].get("articleids", []):
                if id_part["idtype"] == "doi":
                    doi = id_part["value"]
                    break

            # Get citation count and journal ranking concurrently
            logger.debug("Fetching citation count and journal ranking in parallel")
            functions = [
                (get_article_citation_count, doi, self.pmcid),
                (get_journal_ranking, journal_name, self.pmcid),
            ]
            with concurrent.futures.ThreadPoolExecutor() as executor:
                results = list(
                    executor.map(
                        lambda func_args: func_args[0](*func_args[1:]), functions
                    )
                )

            self.json_output["meta_info"]["citation_count"] = results[0]
            self.json_output["journal_ranking"] = results[1]
            logger.debug("Successfully fetched citation count and journal ranking")

            # Process text
            self.json_output = create_sentence_list(self.json_output, self.pmcid)
            logger.debug("Successfully created sentence list")

            self.json_output = split_article_paragraphs(
                self.json_output,
                self.max_split_token_length,
                self.min_split_token_length,
                self.token_overlap,
                self.pmcid,
            )
            logger.debug("Successfully split article paragraphs")

            self.json_output = update_splits(
                self.json_output, self.min_split_token_length
            )
            logger.debug("Successfully updated splits")

            logger.info("Successfully processed article")
            return self.json_output

        except Exception as err:
            logger.error(f"Error building PMC JSON: {err}")
            raise

    def build_from_pmcid(self, pmcid: str) -> Optional[Dict[str, Any]]:
        """Build JSON from PMCID. Returns None if article not found."""
        self.pmcid = pmcid
        logger.info(f"Processing article with PMCID: {pmcid}")

        try:
            xml_data = get_pmc_article_xml(pmcid)
            if xml_data is None:
                logger.error(f"Article not found for PMCID: {pmcid}")
                return None

            try:
                tree = etree.fromstring(xml_data)
                logger.debug("Successfully parsed XML data")
                return self.build_pmc_json(tree)
            except etree.XMLSyntaxError as err:
                logger.error(f"Failed to parse XML data: {err}")
                return None
            except Exception as err:
                logger.error(f"Unexpected error while processing XML: {err}")
                raise
        except Exception as err:
            logger.error(f"Error processing article {pmcid}: {err}")
            return None

    def build_from_xml_file(self, xml_path: str) -> Optional[Dict[str, Any]]:
        """Build JSON from XML file path."""
        logger.info(f"Processing XML file: {xml_path}")

        try:
            tree = etree.parse(xml_path)
            if ".nxml" in xml_path:
                logger.debug("Cleaning XML namespace")
                clean_xml(tree)
            return self.build_pmc_json(tree)
        except (etree.XMLSyntaxError, OSError) as err:
            logger.error(f"Failed to parse XML file {xml_path}: {err}")
            return None
        except Exception as err:
            logger.error(f"Unexpected error processing XML file {xml_path}: {err}")
            return None

    def process_batch(
        self,
        pmcid_list: List[str],
        max_workers: int = 4,
        show_progress: bool = True
    ) -> Dict[str, Any]:
        """Process a batch of PMC articles in parallel with progress tracking.

        Args:
            pmcid_list: List of PMCIDs to process
            max_workers: Maximum number of parallel workers
            show_progress: Whether to show progress bar

        Returns:
            Dict containing:
                - results: List of successfully processed articles
                - failed: List of failed PMCIDs with error messages
                - stats: Processing statistics
        """
        results = []
        failed = []
        stats = {
            "total": len(pmcid_list),
            "successful": 0,
            "failed": 0,
            "skipped": 0
        }

        def process_single(pmcid: str) -> Optional[Dict[str, Any]]:
            try:
                return self.build_from_pmcid(pmcid)
            except Exception as e:
                logger.error(f"Failed to process {pmcid}: {str(e)}")
                return None

        # Create progress bar if requested
        iterator = tqdm(pmcid_list, desc="Processing articles") if show_progress else pmcid_list

        # Process articles in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_pmcid = {executor.submit(process_single, pmcid): pmcid for pmcid in iterator}
            
            for future in concurrent.futures.as_completed(future_to_pmcid):
                pmcid = future_to_pmcid[future]
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                        stats["successful"] += 1
                    else:
                        failed.append({"pmcid": pmcid, "error": "Processing returned None"})
                        stats["failed"] += 1
                except Exception as e:
                    failed.append({"pmcid": pmcid, "error": str(e)})
                    stats["failed"] += 1

        return {
            "results": results,
            "failed": failed,
            "stats": stats
        }
