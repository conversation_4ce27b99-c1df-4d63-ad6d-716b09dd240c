{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Batch Processing and Summarization Example\n", "\n", "This notebook demonstrates how to:\n", "1. Process multiple PMC articles in parallel with progress tracking\n", "2. Generate summaries and extract keywords from the processed articles"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-06-15 21:34:26.549\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m20\u001b[0m - \u001b[34m\u001b[1mSuccessfully loaded spaCy model 'en_core_web_sm'\u001b[0m\n"]}], "source": ["import sys\n", "sys.path.append('..')\n", "\n", "from src.parsers.pmc_parser import PMCParser\n", "from src.parsers.text_processors import generate_summary, extract_keywords\n", "import json\n", "from pprint import pprint"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Initialize the Parser\n", "\n", "First, we'll initialize the PMC parser with our desired configuration:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Initialize parser with default settings\n", "parser = PMCParser(\n", "    max_split_token_length=500,\n", "    min_split_token_length=100,\n", "    token_overlap=10\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Batch Process Articles\n", "\n", "Let's process a list of PMC articles in parallel. We'll use a small list for demonstration, but this works well with larger lists too:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing articles:   0%|          | 0/3 [00:00<?, ?it/s]\u001b[32m2025-06-15 21:41:26.099\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m133\u001b[0m - \u001b[1mProcessing article with PMCID: PMC9210861\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.101\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m133\u001b[0m - \u001b[1mProcessing article with PMCID: PMC9210862\u001b[0m\n", "Processing articles: 100%|██████████| 3/3 [00:00<00:00, 477.35it/s]\u001b[32m2025-06-15 21:41:26.102\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.api.pmc_article_fetcher\u001b[0m:\u001b[36mget_pmc_article_xml\u001b[0m:\u001b[36m36\u001b[0m - \u001b[1mFetching article XML for PMCID: PMC9210861\u001b[0m\n", "\n", "\u001b[32m2025-06-15 21:41:26.104\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m133\u001b[0m - \u001b[1mProcessing article with PMCID: PMC9210863\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.104\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.api.pmc_article_fetcher\u001b[0m:\u001b[36mget_pmc_article_xml\u001b[0m:\u001b[36m36\u001b[0m - \u001b[1mFetching article XML for PMCID: PMC9210862\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.109\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.api.pmc_article_fetcher\u001b[0m:\u001b[36mget_pmc_article_xml\u001b[0m:\u001b[36m36\u001b[0m - \u001b[1mFetching article XML for PMCID: PMC9210863\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.983\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.pmc_article_fetcher\u001b[0m:\u001b[36mget_pmc_article_xml\u001b[0m:\u001b[36m50\u001b[0m - \u001b[34m\u001b[1mSuccessfully retrieved XML data for PMCID: PMC9210862\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.984\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m143\u001b[0m - \u001b[34m\u001b[1mSuccessfully parsed XML data\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.984\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m67\u001b[0m - \u001b[34m\u001b[1mSuccessfully cleaned XML data\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.984\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m71\u001b[0m - \u001b[34m\u001b[1mSuccessfully extracted sections\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.984\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m75\u001b[0m - \u001b[34m\u001b[1mSuccessfully extracted metadata\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.984\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m89\u001b[0m - \u001b[34m\u001b[1mFetching citation count and journal ranking\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.993\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36msrc.parsers.metadata_handlers\u001b[0m:\u001b[36mget_article_citation_count\u001b[0m:\u001b[36m125\u001b[0m - \u001b[33m\u001b[1mNo DOI found for article PMC9210863\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.995\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36msrc.parsers.metadata_handlers\u001b[0m:\u001b[36mget_journal_ranking\u001b[0m:\u001b[36m142\u001b[0m - \u001b[33m\u001b[1mNo journal name found for article PMC9210863\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.995\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m103\u001b[0m - \u001b[34m\u001b[1mSuccessfully fetched citation count and journal ranking\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.995\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m107\u001b[0m - \u001b[34m\u001b[1mSuccessfully created sentence list\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.995\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m116\u001b[0m - \u001b[34m\u001b[1mSuccessfully split article paragraphs\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.999\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m121\u001b[0m - \u001b[34m\u001b[1mSuccessfully updated splits\u001b[0m\n", "\u001b[32m2025-06-15 21:41:26.999\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m123\u001b[0m - \u001b[1mSuccessfully processed article\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.092\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.pmc_article_fetcher\u001b[0m:\u001b[36mget_pmc_article_xml\u001b[0m:\u001b[36m50\u001b[0m - \u001b[34m\u001b[1mSuccessfully retrieved XML data for PMCID: PMC9210863\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.094\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m143\u001b[0m - \u001b[34m\u001b[1mSuccessfully parsed XML data\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.095\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m67\u001b[0m - \u001b[34m\u001b[1mSuccessfully cleaned XML data\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.097\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m71\u001b[0m - \u001b[34m\u001b[1mSuccessfully extracted sections\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.097\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m75\u001b[0m - \u001b[34m\u001b[1mSuccessfully extracted metadata\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.097\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m89\u001b[0m - \u001b[34m\u001b[1mFetching citation count and journal ranking\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.097\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.citation_count\u001b[0m:\u001b[36mget_article_citation_count\u001b[0m:\u001b[36m43\u001b[0m - \u001b[34m\u001b[1mRequesting citation count for DOI 10.2337/dc21-1543 (pmc_id PMC9210863)\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.174\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.pmc_article_fetcher\u001b[0m:\u001b[36mget_pmc_article_xml\u001b[0m:\u001b[36m50\u001b[0m - \u001b[34m\u001b[1mSuccessfully retrieved XML data for PMCID: PMC9210861\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.177\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m143\u001b[0m - \u001b[34m\u001b[1mSuccessfully parsed XML data\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.179\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m67\u001b[0m - \u001b[34m\u001b[1mSuccessfully cleaned XML data\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.181\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m71\u001b[0m - \u001b[34m\u001b[1mSuccessfully extracted sections\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.182\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m75\u001b[0m - \u001b[34m\u001b[1mSuccessfully extracted metadata\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.183\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m89\u001b[0m - \u001b[34m\u001b[1mFetching citation count and journal ranking\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.184\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.citation_count\u001b[0m:\u001b[36mget_article_citation_count\u001b[0m:\u001b[36m43\u001b[0m - \u001b[34m\u001b[1mRequesting citation count for DOI 10.2337/dc21-2034 (pmc_id PMC9210863)\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.773\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.citation_count\u001b[0m:\u001b[36mget_article_citation_count\u001b[0m:\u001b[36m62\u001b[0m - \u001b[34m\u001b[1mRetrieved citation count for DOI 10.2337/dc21-1543: 17\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.773\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m103\u001b[0m - \u001b[34m\u001b[1mSuccessfully fetched citation count and journal ranking\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.823\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 11 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.838\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.citation_count\u001b[0m:\u001b[36mget_article_citation_count\u001b[0m:\u001b[36m62\u001b[0m - \u001b[34m\u001b[1mRetrieved citation count for DOI 10.2337/dc21-2034: 29\u001b[0m\n", "\u001b[32m2025-06-15 21:41:27.840\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m103\u001b[0m - \u001b[34m\u001b[1mSuccessfully fetched citation count and journal ranking\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.089\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 47 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.126\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 47 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.126\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 5 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.160\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 5 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.160\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 4 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.176\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 4 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.205\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 2 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.224\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 2 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.224\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 2 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.245\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 4 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.245\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 4 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.275\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 4 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.275\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 4 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.323\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 9 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.339\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 9 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.339\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 2 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.339\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 2 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.390\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 6 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.390\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 6 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.406\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 3 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.423\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 3 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.505\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 12 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.505\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m107\u001b[0m - \u001b[34m\u001b[1mSuccessfully created sentence list\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.505\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 12 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.505\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m107\u001b[0m - \u001b[34m\u001b[1mSuccessfully created sentence list\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.505\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m127\u001b[0m - \u001b[31m\u001b[1mError building PMC JSON: 'sentence_list'\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.505\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m149\u001b[0m - \u001b[31m\u001b[1mUnexpected error while processing XML: 'sentence_list'\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.505\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m152\u001b[0m - \u001b[31m\u001b[1mError processing article PMC9210861: 'sentence_list'\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.505\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m116\u001b[0m - \u001b[34m\u001b[1mSuccessfully split article paragraphs\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.520\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m121\u001b[0m - \u001b[34m\u001b[1mSuccessfully updated splits\u001b[0m\n", "\u001b[32m2025-06-15 21:41:28.521\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m123\u001b[0m - \u001b[1mSuccessfully processed article\u001b[0m\n"]}], "source": ["# List of PMCIDs to process\n", "pmcid_list = [\n", "    \"PMC9210861\",  # Example article about COVID-19\n", "    \"PMC9210862\",  # Example article about diabetes\n", "    \"PMC9210863\"   # Example article about cancer\n", "]\n", "\n", "# Process articles in parallel with progress tracking\n", "results = parser.process_batch(\n", "    pmcid_list=pmcid_list,\n", "    max_workers=4,  # Number of parallel workers\n", "    show_progress=True  # Show progress bar\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON> Results\n", "\n", "Let's examine the processing results:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing Statistics:\n", "{'failed': 1, 'skipped': 0, 'successful': 2, 'total': 3}\n", "\n", "Failed Articles:\n", "[{'error': 'Processing returned None', 'pmcid': 'PMC9210863'}]\n"]}], "source": ["# Print processing statistics\n", "print(\"Processing Statistics:\")\n", "pprint(results[\"stats\"])\n", "\n", "# Print any failed articles\n", "if results[\"failed\"]:\n", "    print(\"\\nFailed Articles:\")\n", "    pprint(results[\"failed\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Generate Summaries and Extract Keywords\n", "\n", "Now let's process each successful article to generate summaries and extract keywords:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-06-15 21:35:32.482\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mgenerate_summary\u001b[0m:\u001b[36m256\u001b[0m - \u001b[33m\u001b[1mInvalid input text for summarization\u001b[0m\n", "\u001b[32m2025-06-15 21:35:32.482\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mextract_keywords\u001b[0m:\u001b[36m302\u001b[0m - \u001b[33m\u001b[1mInvalid input text for keyword extraction\u001b[0m\n", "\u001b[32m2025-06-15 21:35:32.483\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mgenerate_summary\u001b[0m:\u001b[36m256\u001b[0m - \u001b[33m\u001b[1mInvalid input text for summarization\u001b[0m\n", "\u001b[32m2025-06-15 21:35:32.483\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mextract_keywords\u001b[0m:\u001b[36m302\u001b[0m - \u001b[33m\u001b[1mInvalid input text for keyword extraction\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Processing article: Unknown\n", "\n", "Summary:\n", "None\n", "\n", "Keywords:\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "Processing article: Unknown\n", "\n", "Summary:\n", "None\n", "\n", "Keywords:\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["# Process each successful article\n", "for article in results[\"results\"]:\n", "    print(f\"\\nProcessing article: {article.get('pmcid', 'Unknown')}\")\n", "    \n", "    # Get the full text\n", "    full_text = ' '.join([section.get('text', '') for section in article.get('sections', [])])\n", "    \n", "    # Generate summary\n", "    summary = generate_summary(full_text, num_sentences=3)\n", "    print(\"\\nSummary:\")\n", "    print(summary)\n", "    \n", "    # Extract keywords\n", "    keywords = extract_keywords(full_text, num_keywords=10)\n", "    print(\"\\nKeywords:\")\n", "    print(', '.join(keywords))\n", "    \n", "    print(\"-\" * 80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Save Results\n", "\n", "Let's save the processed articles with their summaries and keywords:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add summaries and keywords to articles\n", "processed_articles = []\n", "for article in results[\"results\"]:\n", "    full_text = ' '.join([section.get('text', '') for section in article.get('sections', [])])\n", "    \n", "    # Add summary and keywords\n", "    article['summary'] = generate_summary(full_text, num_sentences=3)\n", "    article['keywords'] = extract_keywords(full_text, num_keywords=10)\n", "    \n", "    processed_articles.append(article)\n", "\n", "# Save to JSON file\n", "with open('processed_articles.json', 'w') as f:\n", "    json.dump({\n", "        'articles': processed_articles,\n", "        'stats': results['stats'],\n", "        'failed': results['failed']\n", "    }, f, indent=2)\n", "\n", "print(\"Results saved to 'processed_articles.json'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Example: Process a Larger Dataset\n", "\n", "Here's how you could process a larger dataset from a file:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Load PMCIDs from a file\n", "def load_pmcids_from_file(file_path):\n", "    with open(file_path, 'r') as f:\n", "        return [line.strip() for line in f if line.strip()]\n", "\n", "# Example usage:\n", "# pmcid_list = load_pmcids_from_file('pmcid_list.txt')\n", "# results = parser.process_batch(pmcid_list, max_workers=8, show_progress=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-06-15 21:36:46.512\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m133\u001b[0m - \u001b[1mProcessing article with PMCID: PMC9210863\u001b[0m\n", "\u001b[32m2025-06-15 21:36:46.513\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.api.pmc_article_fetcher\u001b[0m:\u001b[36mget_pmc_article_xml\u001b[0m:\u001b[36m36\u001b[0m - \u001b[1mFetching article XML for PMCID: PMC9210863\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.093\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.pmc_article_fetcher\u001b[0m:\u001b[36mget_pmc_article_xml\u001b[0m:\u001b[36m50\u001b[0m - \u001b[34m\u001b[1mSuccessfully retrieved XML data for PMCID: PMC9210863\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.094\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_from_pmcid\u001b[0m:\u001b[36m143\u001b[0m - \u001b[34m\u001b[1mSuccessfully parsed XML data\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.095\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m67\u001b[0m - \u001b[34m\u001b[1mSuccessfully cleaned XML data\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.096\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m71\u001b[0m - \u001b[34m\u001b[1mSuccessfully extracted sections\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.097\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m75\u001b[0m - \u001b[34m\u001b[1mSuccessfully extracted metadata\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.097\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m89\u001b[0m - \u001b[34m\u001b[1mFetching citation count and journal ranking\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.097\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.citation_count\u001b[0m:\u001b[36mget_article_citation_count\u001b[0m:\u001b[36m43\u001b[0m - \u001b[34m\u001b[1mRequesting citation count for DOI 10.2337/dc21-1543 (pmc_id PMC9210863)\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.748\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.api.citation_count\u001b[0m:\u001b[36mget_article_citation_count\u001b[0m:\u001b[36m62\u001b[0m - \u001b[34m\u001b[1mRetrieved citation count for DOI 10.2337/dc21-1543: 17\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.748\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m103\u001b[0m - \u001b[34m\u001b[1mSuccessfully fetched citation count and journal ranking\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.782\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.text_processors\u001b[0m:\u001b[36mget_sentences\u001b[0m:\u001b[36m54\u001b[0m - \u001b[34m\u001b[1mExtracted 8 sentences from text\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.783\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m107\u001b[0m - \u001b[34m\u001b[1mSuccessfully created sentence list\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.784\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m116\u001b[0m - \u001b[34m\u001b[1mSuccessfully split article paragraphs\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.784\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m121\u001b[0m - \u001b[34m\u001b[1mSuccessfully updated splits\u001b[0m\n", "\u001b[32m2025-06-15 21:36:47.784\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.parsers.pmc_parser\u001b[0m:\u001b[36mbuild_pmc_json\u001b[0m:\u001b[36m123\u001b[0m - \u001b[1mSuccessfully processed article\u001b[0m\n"]}, {"data": {"text/plain": ["{'abstract': [{'text': 'To examine diabetes incidence in a diverse cohort of U.S. Hispanic/Latinos.\\nThe Hispanic Community Health Study/Study of Latinos is a prospective cohort study with participants aged 18–74 years from four U.S. metropolitan areas. Participants were assessed for diabetes at the baseline examination (2008–2011), annually via telephone interview, and at a second examination (2014–2017).\\nA total of 11,619 participants returned for the second examination. The overall age-adjusted diabetes incidence rate was 22.1 cases/1,000 person-years. The incidence was high among those with Puerto Rican and Mexican backgrounds as well as those aged ≥45 years and with a BMI ≥30 kg/m^2. Significant differences in diabetes awareness, treatment, and health insurance coverage, but not glycemic control, were observed across Hispanic/Latino background groups, age groups, and BMI categories.\\nDifferences in diabetes incidence by Hispanic/Latino background, age, and BMI suggest the susceptibility of these factors.',\n", "   'section_title': 'Abstract',\n", "   'section_type': 'ABSTRACT'}],\n", " 'body_text': [None],\n", " 'conclusion': [None],\n", " 'discussion': [None],\n", " 'meta_info': {'title': 'Diabetes Incidence Among Hispanic/Latino Adults in the Hispanic Community Health Study/Study of Latinos (HCHS/SOL)',\n", "  'kwd': '',\n", "  'authors': [{'first': '<PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON>',\n", "    'middle': [],\n", "    'last': 'Schneiderman',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': 'Yanping',\n", "    'middle': [],\n", "    'last': 'Teng',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON><PERSON><PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': 'Rebeca A.',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON><PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''},\n", "   {'first': '<PERSON><PERSON>',\n", "    'middle': [],\n", "    'last': '<PERSON><PERSON><PERSON>-<PERSON>',\n", "    'suffix': '',\n", "    'affiliation': {},\n", "    'email': ''}],\n", "  'articleids': [{'idtype': 'pmcid', 'value': 'PMC9210863'},\n", "   {'idtype': 'pmcid-ver', 'value': 'PMC9210863.1'},\n", "   {'idtype': 'pmcaid', 'value': '9210863'},\n", "   {'idtype': 'pmcaiid', 'value': '9210863'},\n", "   {'idtype': 'pmid', 'value': '35506707'},\n", "   {'idtype': 'doi', 'value': '10.2337/dc21-1543'},\n", "   {'idtype': 'publisher-id', 'value': '211543'}],\n", "  'fulljournalname': 'Diabetes Care',\n", "  'source': 'Diabetes Care',\n", "  'ppubdate': '2022 Jun',\n", "  'epubdate': '2022 May 27',\n", "  'volume': '45',\n", "  'issue': '6',\n", "  'pages': '1482-1485',\n", "  'citation_count': 17},\n", " 'journal_ranking': {'Journal': 'Diabetes Care',\n", "  'Articles': 21300,\n", "  'Citations': 1700000,\n", "  'Impact Factor': 9.1},\n", " 'article_splits': [{'PMC9210863_abstract_split_0': 'To examine diabetes incidence in a diverse cohort of U.S. Hispanic/Latinos. The Hispanic Community Health Study/Study of Latinos is a prospective cohort study with participants aged 18–74 years from four U.S. metropolitan areas. Participants were assessed for diabetes at the baseline examination (2008–2011), annually via telephone interview, and at a second examination (2014–2017). A total of 11,619 participants returned for the second examination. The overall age-adjusted diabetes incidence rate was 22.1 cases/1,000 person-years. The incidence was high among those with Puerto Rican and Mexican backgrounds as well as those aged ≥45 years and with a BMI ≥30 kg/m^2. Significant differences in diabetes awareness, treatment, and health insurance coverage, but not glycemic control, were observed across Hispanic/Latino background groups, age groups, and BMI categories. Differences in diabetes incidence by Hispanic/Latino background, age, and BMI suggest the susceptibility of these factors.'}]}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["parser.build_from_pmcid('PMC9210863')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}