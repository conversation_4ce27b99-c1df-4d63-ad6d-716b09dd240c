{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# get pmcids articles from pubmed"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "# load project root folder\n", "sys.path.append(os.path.abspath(os.path.join(os.path.dirname(\"__file__\"), \"..\")))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from src.utils.pmc_fetch_ftp import PMCFetchFtp"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["pmc_id_list = [\n", "    \"PMC10011651\",\n", "    \"PMC10012101\",\n", "    \"PMC10012134\",\n", "    \"PMC10015783\",\n", "    \"PMC10032221\",\n", "    \"PMC10032446\",\n", "    \"PMC10034955\",\n", "    \"PMC10034960\",\n", "    \"PMC10034961\",\n", "    \"PMC10036460\",\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get the articles from PMC ftp server using API\n", "\n", "This method is suitable for downloading a short list of articles. it downloads the metadata and the pdf and xml files one by one."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-06-11 21:17:37.554\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10011651\u001b[0m\n", "\u001b[32m2025-06-11 21:18:09.634\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10012101\u001b[0m\n", "\u001b[32m2025-06-11 21:18:45.281\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10012134\u001b[0m\n", "\u001b[32m2025-06-11 21:19:22.870\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10015783\u001b[0m\n", "\u001b[32m2025-06-11 21:19:57.940\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10032221\u001b[0m\n", "\u001b[32m2025-06-11 21:20:34.367\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10032446\u001b[0m\n", "\u001b[32m2025-06-11 21:21:06.344\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10034955\u001b[0m\n", "\u001b[32m2025-06-11 21:21:38.494\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10034960\u001b[0m\n", "\u001b[32m2025-06-11 21:22:10.608\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10034961\u001b[0m\n", "\u001b[32m2025-06-11 21:22:44.948\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10036460\u001b[0m\n"]}, {"data": {"text/plain": ["[]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["PMCFetchFtp.download_articles(\n", "    pmcid_list=pmc_id_list, save_path=\"../data/article_ftp\", use_api=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get articles from PMC ftp server using index files, then download the articles from the ftp server\n", "\n", "This method is suitable for downloading very long lists of articles. It uses the index files to get the path to the articles on the ftp server. It is much faster than using the API."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-06-11 21:22:44.967\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles_from_ftp\u001b[0m:\u001b[36m130\u001b[0m - \u001b[1mReading the index file: ../data/oa_file_list.csv\u001b[0m\n", "\u001b[32m2025-06-11 21:22:49.946\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles_from_ftp\u001b[0m:\u001b[36m144\u001b[0m - \u001b[1mReading the commercial use index file: ../data/oa_comm_use_file_list.csv\u001b[0m\n", "\u001b[32m2025-06-11 21:23:28.276\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10011651\u001b[0m\n", "\u001b[32m2025-06-11 21:24:00.405\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10012101\u001b[0m\n", "\u001b[32m2025-06-11 21:24:33.840\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10012134\u001b[0m\n", "\u001b[32m2025-06-11 21:25:09.312\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10015783\u001b[0m\n", "\u001b[32m2025-06-11 21:25:42.016\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10032221\u001b[0m\n", "\u001b[32m2025-06-11 21:26:15.424\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10032446\u001b[0m\n", "\u001b[32m2025-06-11 21:26:48.312\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10034955\u001b[0m\n", "\u001b[32m2025-06-11 21:27:20.521\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10034960\u001b[0m\n", "\u001b[32m2025-06-11 21:27:52.734\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10034961\u001b[0m\n", "\u001b[32m2025-06-11 21:28:26.261\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36msrc.utils.pmc_fetch_ftp\u001b[0m:\u001b[36mdownload_articles\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mDownloaded and extracted PMC10036460\u001b[0m\n"]}, {"data": {"text/plain": ["[]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["PMCFetchFtp.download_articles(\n", "    pmcid_list=pmc_id_list,\n", "    save_path=\"../data/article_ftp_2\",\n", "    path_index_file=\"../data/oa_file_list.csv\",\n", "    path_comm_index_file=\"../data/oa_comm_use_file_list.csv\",\n", "    include_xml=False,\n", "    use_api=False,\n", ")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}