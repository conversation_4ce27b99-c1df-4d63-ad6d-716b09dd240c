# PMC Parser

A Python package for extracting, processing, and analyzing articles from PubMed Central (PMC). This project provides tools to:

- Parse PMC articles by PMCID or from local XML files into structured JSON.
- Search for PMC articles using keywords or advanced queries.
- Download articles (PDF/XML) from the PMC FTP server using PMCIDs.
- Process articles in batch with parallel processing and progress tracking.
- Generate article summaries and extract key terms.
- Enhanced error handling and logging throughout the pipeline.

## Features

- Extracts article metadata, sections, and text from PMC XML.
- Retrieves citation counts and journal rankings.
- Splits articles into manageable text chunks for downstream processing.
- Utilities for searching PMC and bulk downloading articles.
- Batch processing with parallel execution and progress tracking.
- Article summarization using TF-IDF based sentence scoring.
- Keyword extraction using TF-IDF analysis.
- Comprehensive error handling and logging.

## Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/hsaglamlar/pmc_parser.git
   cd pmc_parser
   ```

2. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

3. (Optional) Download the spaCy English model if not installed automatically:

   ```bash
   python -m spacy download en_core_web_sm
   ```

## Project Structure

- `src/parsers/pmc_parser.py`: Main parser for PMC articles.
- `src/utils/pmc_search.py`: Utility for searching PMC articles.
- `src/utils/pmc_fetch_ftp.py`: Utility for downloading articles from the PMC FTP server.
- `src/parsers/text_processors.py`: Text processing utilities including summarization.
- `examples/`: Example notebooks and sample data.

## Usage

### 1. Parse PMC Articles to JSON

You can parse articles by PMCID or from a local XML file:

```python
from src.parsers.pmc_parser import PMCParser

# Initialize the parser
pmc_parser = PMCParser(
    max_split_token_length=500,
    min_split_token_length=100,
    token_overlap=10,
)

# Parse by PMCID
doc_json = pmc_parser.build_from_pmcid("PMC9210861")

# Parse from a local XML file
doc_json = pmc_parser.build_from_xml_file("examples/sample_xml/main.xml")

# Save to JSON file
import json
with open("./data/parsed_article.json", "w") as f:
    json.dump(doc_json, f, indent=4)
```

### 2. Batch Processing

Process multiple articles in parallel with progress tracking:

```python
pmcid_list = ["PMC9210861", "PMC9210862", "PMC9210863"]
results = pmc_parser.process_batch(
    pmcid_list,
    max_workers=4,
    show_progress=True
)

# Access results
successful_articles = results["results"]
failed_articles = results["failed"]
stats = results["stats"]
```

### 3. Article Summarization and Keyword Extraction

Generate summaries and extract keywords from articles:

```python
from src.parsers.text_processors import generate_summary, extract_keywords

# Generate a 3-sentence summary
summary = generate_summary(article_text, num_sentences=3)

# Extract top 10 keywords
keywords = extract_keywords(article_text, num_keywords=10)
```

### 4. Search for PMC Articles

You can search PMC using keywords or advanced queries:

```python
from src.utils.pmc_search import PMCSearch

query_text = "(diabetes type 2 with heart failure and kidney disease) AND open access[filter]"
params = {
    "sort": "pub_date",
    "email": "your_mail@your_mail",
    "tool": "chatbot",
    "reldate": "7",  # last 7 days
    "retmax": "1000",
}

pmc_id_list = PMCSearch.search(query_text, params)
print(pmc_id_list)
```

#### Batch Search with Keywords File

```python
import pandas as pd
from src.utils.pmc_search import PMCSearch

def search_keywords(keyword_csv_path, search_params):
    category_to_keywords = pd.read_csv(keyword_csv_path).to_dict('list')
    category_to_articles = {}
    for category_name in category_to_keywords:
        articles_for_category = []
        for keyword in category_to_keywords[category_name]:
            search_results = PMCSearch.search(keyword, search_params)
            articles_for_category.extend(search_results)
        unique_articles = list(set(articles_for_category))
        category_to_articles[category_name] = unique_articles
    return category_to_articles

category_article_dict = search_keywords('./examples/keywords.csv', params)
```

### 5. Download Articles from PMC FTP

You can download articles by PMCID using the FTP utility:

```python
from src.utils.pmc_fetch_ftp import PMCFetchFtp

pmc_id_list = [
    "PMC10011651", "PMC10012101", "PMC10012134"
]

# Download using the API (for short lists)
PMCFetchFtp.download_articles(
    pmcid_list=pmc_id_list, save_path="./data/article_ftp", use_api=True
)

# Download using index files (for large lists)
PMCFetchFtp.download_articles(
    pmcid_list=pmc_id_list,
    save_path="./data/article_ftp_2",
    path_index_file="./data/oa_file_list.csv",
    path_comm_index_file="./data/oa_comm_use_file_list.csv",
    include_xml=False,
    use_api=False,
)
```

## Example Notebooks

See the `examples/` directory for Jupyter notebooks demonstrating full workflows:

- `examples/pmc_parser_run.ipynb`: Parsing articles.
- `examples/pmc_search_run.ipynb`: Searching PMC.
- `examples/pmc_fetch_ftp_run.ipynb`: Downloading articles from FTP.
- `examples/batch_processing.ipynb`: Batch processing articles.
- `examples/summarization.ipynb`: Article summarization and keyword extraction.

## License

[MIT License](LICENSE)

## Acknowledgments

- PubMed Central (PMC)
- spaCy, lxml, pandas, scikit-learn, and other open-source libraries
