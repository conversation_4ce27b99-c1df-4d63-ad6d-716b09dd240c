{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "# load project root folder\n", "sys.path.append(os.path.abspath(os.path.join(os.path.dirname(\"__file__\"), \"..\")))\n", "\n", "\n", "\n", "from src.utils.pmc_search import PMCSearch\n", "import pandas as pd"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# basic search usage \n", "\n", "set the query text and parameters\n", "\n", "query text can be a keyword or a combination of keywords and filters, you can use the pubmed advanced search to create your query text (https://www.ncbi.nlm.nih.gov/pmc/advanced/)\n", "\n", "parameters are the parameters you want to add to the query, you can find the list of parameters in the pubmed documentation https://www.ncbi.nlm.nih.gov/books/NBK25499/#chapter4.ESearch\n", "\n", "for example if you want to get the articles for the last 7 days, you can use the reldate parameter and set it to 7\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["38737\n"]}], "source": ["# get all records from 2000/01/01 to now and \n", "# return results as 10000 records per request\n", "params_1 = {\n", "    \"sort\": \"pub_date\",\n", "    \"email\": \"your_mail@your_mail\",\n", "    \"tool\": \"chatbot\",\n", "    \"mindate\": \"2025/01/01\",\n", "    \"retmax\": \"1000\",\n", "}\n", "\n", "# get all records for last 7 days and \n", "# return results as 1000 records per request\n", "params_2 = {\n", "    \"sort\": \"pub_date\",\n", "    \"email\": \"your_mail@your_mail\",\n", "    \"tool\": \"chatbot\",\n", "    \"reldate\": \"7\",\n", "    \"retmax\": \"1000\",\n", "}\n", "\n", "query_text = \"(diabetes type 2 with heart failure and kidney disease) AND open access[filter]\"\n", "pmc_id_list = PMCSearch.search(query_text, params_1)\n", "\n", "print(len(list(set(pmc_id_list))))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# search usage with keywords file"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def search_keywords(keyword_csv_path, search_params):\n", "    # Read the CSV file where each column is a category and each cell is a keyword\n", "    # first row is the category name\n", "    category_to_keywords = pd.read_csv(keyword_csv_path).to_dict('list')\n", "    category_to_articles = {}\n", "    for category_name in list(category_to_keywords.keys()):\n", "        try:\n", "            print(category_name)\n", "            articles_for_category = []\n", "            for keyword in category_to_keywords[category_name]:\n", "                search_results = PMCSearch.search(keyword, search_params)\n", "                articles_for_category.extend(search_results)\n", "            # Remove duplicate articles\n", "            unique_articles = list(set(articles_for_category))\n", "            category_to_articles[category_name] = unique_articles\n", "        except Exception:\n", "            print(f\"Failed to retrieve articles in {category_name}\")\n", "            continue\n", "    return category_to_articles"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Type_2_Diabetes\n", "Essential_Hypertension\n", "Breast_Cancer\n", "Lung_Cancer\n", "Alzheimers_Disease\n", "Metabolic_Syndrome\n", "Major_Depression\n", "Obesity\n", "Fatty_Liver_Disease\n", "Myocardial_Infarction\n"]}], "source": ["category_article_dict = search_keywords('./keywords.csv', params_2)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Type_2_Diabetes : 4067\n", "Essential_Hypertension : 3007\n", "Breast_Cancer : 1928\n", "Lung_Cancer : 2219\n", "Alzheimers_Disease : 2837\n", "Metabolic_Syndrome : 5033\n", "Major_Depression : 2498\n", "Obesity : 3845\n", "Fatty_Liver_Disease : 1657\n", "Myocardial_Infarction : 2418\n"]}], "source": ["for category in list(category_article_dict.keys()):\n", "    print(category,\":\", len(category_article_dict[category]))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["['12126188', '12137065', '12130331', '12125126', '12137913']"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["category_article_dict[\"Myocardial_Infarction\"][:5]"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}