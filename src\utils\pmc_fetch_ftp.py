"""Purpose: Fetch articles from PMC ftp server"""

import os
from typing import List, Optional, Tuple
import urllib.request
import tarfile
import requests
from bs4 import BeautifulSoup
import pandas as pd  # type: ignore
from loguru import logger  # type: ignore


class PMCFetchFtp:
    """Class for fetching articles from PMC ftp server"""

    def __init__(self):
        pass

    @staticmethod
    def download_articles(
        pmcid_list: List[str],
        save_path: str,
        path_index_file: Optional[str] = None,
        path_comm_index_file: Optional[str] = None,
        include_xml: bool = True,
        use_api: bool = False,
    ) -> List[str]:
        """Get the articles from PMC from ftp://ftp.ncbi.nlm.nih.gov/pub/pmc/
        and save them as pdf and xml files.

        Args:
            pmcid_list (list): A list of PMCID
            save_path (str): The path to save the pdf and xml files
            path_index_file (str): The path to the index file for the articles
            path_comm_index_file (str): The path to the commercial use index file for the articles

        Returns:
            failed_pmcids (list): A list of PMCID that failed to download
        """
        if use_api:
            url_list = PMCFetchFtp.get_article_urls_from_api(pmcid_list)
        else:
            url_list = PMCFetchFtp.get_articles_from_index_file(
                pmcid_list, path_index_file, path_comm_index_file
            )

        # Create directories robustly
        os.makedirs(save_path, exist_ok=True)
        tars_path = os.path.join(save_path, "tars")
        os.makedirs(tars_path, exist_ok=True)

        failed_pmcids = []
        for pmcid, url in url_list:
            if not url:
                logger.error(f"No URL found for {pmcid}, skipping download.")
                failed_pmcids.append(pmcid)
                continue
            try:
                tar_destination = os.path.join(tars_path, f"{pmcid}.tar.gz")
                decompress_destination = save_path
                # Download file with context manager
                try:
                    with urllib.request.urlopen(url) as response, open(
                        tar_destination, "wb"
                    ) as out_file:
                        out_file.write(response.read())
                except Exception as e:
                    logger.error(f"Failed to download {url} for {pmcid}: {e}")
                    failed_pmcids.append(pmcid)
                    continue

                # Decompress the tar file
                try:
                    with tarfile.open(tar_destination, "r:gz") as tar_file:
                        file_names = tar_file.getnames()
                        for file_name in file_names:
                            if file_name.endswith("pdf") or (
                                include_xml and file_name.endswith("xml")
                            ):
                                try:
                                    tar_file.extract(
                                        file_name, path=decompress_destination
                                    )
                                except Exception as e:
                                    logger.error(
                                        f"Failed to extract {file_name} from {tar_destination}: {e}"
                                    )
                    logger.info(f"Downloaded and extracted {pmcid}")
                except Exception as e:
                    logger.error(f"An error occurred at decompressing {pmcid}: {e}")
                    failed_pmcids.append(pmcid)
                finally:
                    # Clean up tar file
                    try:
                        if os.path.exists(tar_destination):
                            os.remove(tar_destination)
                    except Exception as e:
                        logger.warning(
                            f"Failed to remove temporary tar file {tar_destination}: {e}"
                        )
            except Exception as e:
                logger.error(f"An error occurred at downloading {pmcid}: {e}")
                failed_pmcids.append(pmcid)
        return failed_pmcids

    @staticmethod
    def get_article_urls_from_api(
        pmcid_list: List[str],
    ) -> List[Tuple[str, Optional[str]]]:
        """Download the articles from the PubMed API.
        this function is suitable for downloading a short list of articles.
        """
        pmc_urls = []
        for pmcid in pmcid_list:
            try:
                url = PMCFetchFtp.get_path_from_api(pmcid)
                pmc_urls.append((pmcid, url))
            except Exception as e:
                logger.error(f"An error occurred at getting path to {pmcid}: {e}")
                pmc_urls.append((pmcid, None))
                continue
        return pmc_urls

    @staticmethod
    def get_articles_from_index_file(
        pmcid_list: List[str],
        path_index_file: Optional[str],
        path_comm_index_file: Optional[str],
    ) -> List[Tuple[str, Optional[str]]]:
        """Download the articles from the ftp server.
        this function is suitable for downloading very long lists of articles.
        """
        # Validate index file paths
        if not path_index_file:
            raise ValueError("path_index_file must be provided.")
        if not path_comm_index_file:
            raise ValueError("path_comm_index_file must be provided.")
        # Read or download the index file
        try:
            if not os.path.exists(path_index_file):
                raise FileNotFoundError(
                    f"The path_index_file {path_index_file} is not valid"
                )
            logger.info(f"Reading the index file: {path_index_file}")
            article_list = pd.read_csv(
                path_index_file, usecols=["Accession ID", "File"]
            )
        except Exception:
            logger.info("Downloading the index file from the ftp server")
            url = "https://ftp.ncbi.nlm.nih.gov/pub/pmc/oa_file_list.csv"
            article_list = pd.read_csv(url, usecols=["Accession ID", "File"])
            article_list.to_csv(path_index_file, index=False)
            logger.info(f"Downloaded the index file and saved it to {path_index_file}")
        # Read or download the commercial use index file
        try:
            if not os.path.exists(path_comm_index_file):
                raise FileNotFoundError(
                    f"The path_comm_index_file {path_comm_index_file} is not valid"
                )
            logger.info(
                f"Reading the commercial use index file: {path_comm_index_file}"
            )
            article_comm_list = pd.read_csv(
                path_comm_index_file, usecols=["Accession ID", "File"]
            )
        except Exception:
            logger.info("Downloading the commercial use index file from the ftp server")
            url = "https://ftp.ncbi.nlm.nih.gov/pub/pmc/oa_comm_use_file_list.csv"
            article_comm_list = pd.read_csv(url, usecols=["Accession ID", "File"])
            article_comm_list.to_csv(path_comm_index_file, index=False)
            logger.info(
                f"Downloaded the commercial use index file and saved it to {path_comm_index_file}"
            )
        # Ensure pmcids start with PMC
        pmcid_list = [
            pmcid if pmcid.startswith("PMC") else f"PMC{pmcid}" for pmcid in pmcid_list
        ]
        pmc_urls = []
        for pmcid in pmcid_list:
            try:
                url = None
                file_name = article_list.loc[
                    article_list["Accession ID"] == pmcid, "File"
                ]
                if file_name.empty:
                    file_name = article_comm_list.loc[
                        article_comm_list["Accession ID"] == pmcid, "File"
                    ]
                if not file_name.empty:
                    file_name = file_name.values[0]
                    url = f"ftp://ftp.ncbi.nlm.nih.gov/pub/pmc/{file_name}"
                if url is None:
                    raise ValueError(f"The article {pmcid} is not Open Access")
                pmc_urls.append((pmcid, url))
            except Exception as e:
                logger.error(f"An error occurred at getting path to {pmcid}: {e}")
                pmc_urls.append((pmcid, None))
                continue
        return pmc_urls

    @staticmethod
    def get_path_from_api(pmc_id: str) -> Optional[str]:
        """Get the path to the article ftp server from the PubMed API

        Args:
            pmc_id (str): The PMCID of the article

        Returns:
            path (str): The path to the article
        """
        pmc_id = pmc_id if pmc_id.startswith("PMC") else f"PMC{pmc_id}"
        url = f"https://www.ncbi.nlm.nih.gov/pmc/utils/oa/oa.fcgi?id={pmc_id}"
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, "xml")
            record = soup.find("record")
            if not record:
                logger.error(f"No record found for {pmc_id}")
                return None
            link = record.find("link", format="tgz")
            if not link or not link.get("href"):
                logger.error(f"No tgz link found for {pmc_id}")
                return None
            href = link["href"]
        except Exception as e:
            logger.error(f"An error occurred at getting path to {pmc_id}: {e}")
            return None
        return href
