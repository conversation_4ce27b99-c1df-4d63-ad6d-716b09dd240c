"""Text processing utilities for PMC parser."""

import re
from typing import List, Dict, Any, Optional
from loguru import logger

import spacy  # type: ignore
import tiktoken
from collections import Counter
from sklearn.feature_extraction.text import TfidfVectorizer # type: ignore
import numpy as np

# Load spaCy model once at module level
try:
    # check if model is installed, if not install it
    if "en_core_web_sm" not in spacy.util.get_installed_models():
        logger.info("Downloading spaCy model 'en_core_web_sm'")
        spacy.cli.download("en_core_web_sm")
    nlp = spacy.load("en_core_web_sm")
    logger.debug("Successfully loaded spaCy model 'en_core_web_sm'")
except Exception as e:
    logger.error(f"Failed to load spaCy model: {e}")
    logger.warning("Using blank English model as fallback")
    nlp = spacy.blank("en")


def clean_text(text: str) -> str:
    """Clean text by removing extra whitespace and special characters."""
    text = re.sub(r"\n {3,}", " ", text.strip()).replace("^None", "")
    text = re.sub(r"\n\t+", " ", text)
    return text


def get_sentences(text: str) -> List[str]:
    """Extract sentences from text using spaCy.

    Args:
        text: The text to extract sentences from

    Returns:
        List of sentences as strings

    Examples:
        >>> get_sentences("Hello world. This is a test.")
        ['Hello world.', 'This is a test.']
    """
    if not text or not isinstance(text, str):
        logger.warning(f"Invalid input text: {type(text)}")
        return []

    try:
        doc = nlp(text)
        sentences = [sent.text.strip() for sent in doc.sents if sent.text.strip()]
        logger.debug(f"Extracted {len(sentences)} sentences from text")
        return sentences
    except Exception as e:
        logger.error(f"Error processing text with spaCy: {e}")
        logger.warning("Falling back to simple sentence splitting")
        # Fallback to simple sentence splitting
        return _simple_sentence_split(text)


def _simple_sentence_split(text: str) -> List[str]:
    """Simple fallback sentence splitter using punctuation.

    Args:
        text: Text to split into sentences

    Returns:
        List of sentences
    """
    if not text:
        return []

    # Split on common sentence-ending punctuation
    sentences = []
    for sent in text.replace("!", ".").replace("?", ".").split("."):
        sent = sent.strip()
        if sent:
            sentences.append(sent + ".")

    return sentences


def group_sentences(
    sentences: List[str], max_tokens: int, overlap_token_length: int
) -> tuple[List[List[str]], List[int]]:
    """Group sentences into chunks with specified token limits and overlap."""
    grouped_sentences: List[List[str]] = []
    current_group: List[str] = []
    grouped_sentences_lengths: List[int] = []
    token_count = 0
    tokenizer = tiktoken.get_encoding("p50k_base")

    for sentence in sentences:
        tokens = tokenizer.encode(sentence, disallowed_special=())
        sentence_length = len(tokens)

        if token_count + sentence_length <= max_tokens:
            current_group.append(sentence)
            token_count += sentence_length
        else:
            grouped_sentences.append(current_group)
            grouped_sentences_lengths.append(token_count)
            current_group = current_group[-overlap_token_length:]
            current_group.append(sentence)
            token_count = sum(
                len(tokenizer.encode(sent, disallowed_special=()))
                for sent in current_group
            )

    if current_group:
        grouped_sentences.append(current_group)
        grouped_sentences_lengths.append(token_count)

    return grouped_sentences, grouped_sentences_lengths


def create_sentence_list(json_data: Dict[str, Any], pmcid: str) -> Dict[str, Any]:
    """Create sentence lists for each section of the article."""
    # Process abstract, conclusion and discussion
    for part in ["abstract", "conclusion", "discussion"]:
        sent_id = 0
        if (json_data[part][0] is not None) and (
            "sentence_list" not in json_data[part][0]
        ):
            sentences = get_sentences(json_data[part][0]["text"])
            sentence_list_with_id = [
                {f"{pmcid}_{part}_sent_{sent_id}": sentence}
                for sent_id, sentence in enumerate(sentences)
            ]
            json_data[part][0]["sentence_list"] = sentence_list_with_id

    # Process body text
    if json_data["body_text"][0] is not None:
        for section in json_data["body_text"]:
            if "sentence_list" not in section:
                sentences = get_sentences(section["text"])
                sentence_list_with_id = [
                    {
                        f"{pmcid}_parag_{section['paragraph_id']}_sent_{sent_id}": sentence
                    }
                    for sent_id, sentence in enumerate(sentences)
                ]
                section["sentence_list"] = sentence_list_with_id

    return json_data


def _process_section_splits(
    sentence_list: List[str],
    max_split_token_length: int,
    min_split_token_length: int,
    token_overlap: int,
    split_key_template: str,
) -> List[Dict[str, str]]:
    """Helper function to process splits for a section."""
    splits, splits_lengths = group_sentences(
        sentence_list, max_split_token_length, token_overlap
    )

    split_list = []
    split_id = 0
    for i, split in enumerate(splits):
        if (splits_lengths[i] > min_split_token_length) or (i == 0):
            split_key = split_key_template.format(split_id=split_id)
            split_list.append({split_key: " ".join(split)})
            split_id += 1
        else:
            previous_split = split_list[-1]
            previous_split[list(previous_split.keys())[0]] += " ".join(split)

    return split_list


def split_article_paragraphs(
    json_data: Dict[str, Any],
    max_split_token_length: int,
    min_split_token_length: int,
    token_overlap: int,
    pmcid: str,
) -> Dict[str, Any]:
    """Split article into paragraphs with token length constraints."""
    article_splits = []

    # Split abstract, conclusion and discussion
    for part in ["abstract", "discussion", "conclusion"]:
        if json_data[part][0] is not None:
            sentence_list = [
                list(sentence.values())[0]
                for sentence in json_data[part][0]["sentence_list"]
            ]
            split_key_template = f"{pmcid}_{part}_split_{{split_id}}"
            split_list = _process_section_splits(
                sentence_list, max_split_token_length, min_split_token_length,
                token_overlap, split_key_template
            )
            article_splits.extend(split_list)
            del json_data[part][0]["sentence_list"]

    # Split body text
    if json_data["body_text"][0] is not None:
        for section in json_data["body_text"]:
            sentence_list = [
                list(sentence.values())[0] for sentence in section["sentence_list"]
            ]
            split_key_template = f"{pmcid}_parag_{section['paragraph_id']}_split_{{split_id}}"
            split_list = _process_section_splits(
                sentence_list, max_split_token_length, min_split_token_length,
                token_overlap, split_key_template
            )
            article_splits.extend(split_list)
            del section["sentence_list"]

    json_data["article_splits"] = article_splits
    return json_data


def update_splits(
    json_data: Dict[str, Any], min_split_token_length: int
) -> Dict[str, Any]:
    """Update splits to ensure minimum token length."""
    tokenizer = tiktoken.get_encoding("p50k_base")
    splits_tokens_counts = [
        len(tokenizer.encode(list(split.values())[0], disallowed_special=()))
        for split in json_data["article_splits"]
    ]

    for i, split in reversed(list(enumerate(json_data["article_splits"]))):
        if (
            ("paragraph" in list(split.keys())[0])
            and (splits_tokens_counts[i] < min_split_token_length)
            and (i > 0)
        ):
            # Merge with previous split
            previous_split = json_data["article_splits"][i - 1]
            previous_split[list(previous_split.keys())[0]] += split[
                list(split.keys())[0]
            ]
            splits_tokens_counts[i - 1] += splits_tokens_counts[i]
            del json_data["article_splits"][i]

    return json_data


def generate_summary(text: str, num_sentences: int = 3) -> Optional[str]:
    """Generate a summary of the text using TF-IDF based sentence scoring.
    
    Args:
        text: The text to summarize
        num_sentences: Number of sentences to include in summary
        
    Returns:
        Optional[str]: The generated summary or None if processing failed
    """
    if not text or not isinstance(text, str):
        logger.warning("Invalid input text for summarization")
        return None
        
    try:
        # Get sentences
        sentences = get_sentences(text)
        if not sentences:
            logger.warning("No sentences found for summarization")
            return None
            
        # Calculate TF-IDF scores
        vectorizer = TfidfVectorizer(stop_words='english')
        tfidf_matrix = vectorizer.fit_transform(sentences)
        
        # Calculate sentence scores based on TF-IDF
        sentence_scores = []
        for i in range(len(sentences)):
            score = np.mean(tfidf_matrix[i].toarray())
            sentence_scores.append((i, score))
            
        # Get top sentences
        top_sentences = sorted(sentence_scores, key=lambda x: x[1], reverse=True)[:num_sentences]
        top_sentences = sorted(top_sentences, key=lambda x: x[0])  # Sort by original position
        
        # Combine sentences into summary
        summary = ' '.join([sentences[i] for i, _ in top_sentences])
        
        logger.debug(f"Generated summary with {len(top_sentences)} sentences")
        return summary
        
    except Exception as e:
        logger.error(f"Error generating summary: {str(e)}")
        return None


def extract_keywords(text: str, num_keywords: int = 10) -> List[str]:
    """Extract key terms from text using TF-IDF.
    
    Args:
        text: The text to extract keywords from
        num_keywords: Number of keywords to extract
        
    Returns:
        List[str]: List of extracted keywords
    """
    if not text or not isinstance(text, str):
        logger.warning("Invalid input text for keyword extraction")
        return []
        
    try:
        # Get sentences
        sentences = get_sentences(text)
        if not sentences:
            return []
            
        # Calculate TF-IDF
        vectorizer = TfidfVectorizer(stop_words='english', max_features=num_keywords)
        tfidf_matrix = vectorizer.fit_transform(sentences)
        
        # Get feature names (words)
        feature_names = vectorizer.get_feature_names_out()
        
        # Calculate word scores
        word_scores = []
        for i in range(len(feature_names)):
            score = np.mean(tfidf_matrix[:, i].toarray())
            word_scores.append((feature_names[i], score))
            
        # Get top keywords
        keywords = [word for word, _ in sorted(word_scores, key=lambda x: x[1], reverse=True)[:num_keywords]]
        
        logger.debug(f"Extracted {len(keywords)} keywords")
        return keywords
        
    except Exception as e:
        logger.error(f"Error extracting keywords: {str(e)}")
        return []
