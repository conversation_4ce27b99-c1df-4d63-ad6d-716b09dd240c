"""XML processing utilities for PMC articles."""

from lxml import etree
from itertools import chain


def remove_namespace(tree) -> None:
    """Strip namespace from parsed XML"""
    for node in tree.iter():
        try:
            has_namespace = node.tag.startswith("{")
        except AttributeError:
            continue  # node.tag is not a string (node is a comment or similar)
        if has_namespace:
            node.tag = node.tag.split("}", 1)[1]


def stringify_children(node):
    """
    Filters and removes possible Nones in texts and tails
    ref: http://stackoverflow.com/questions/4624062/get-all-text-inside-a-tag-in-lxml
    """
    parts = (
        [node.text]
        + list(chain(*([c.text, c.tail] for c in node.getchildren())))
        + [node.tail]
    )
    return "".join(filter(None, parts)).strip()


def clean_xml(doc):
    """Clean the XML data by removing the namespace and removing the unnecessary tags"""

    # Remove the namespace
    remove_namespace(doc)

    # Check for tables, figures, formulas within <sec/p> tags and remove them
    n = doc.findall(".//sec/p/table-wrap")
    if len(n) > 0:
        for node in n:
            node.getparent().remove(node)

    n = doc.findall(".//sec/p/fig")
    if len(n) > 0:
        for node in n:
            node.getparent().remove(node)

    n = doc.findall(".//disp-formula")
    if len(n) > 0:
        for node in n:
            node.getparent().remove(node)

    n = doc.findall(".//back")
    if len(n) > 0:
        for node in n:
            node.getparent().remove(node)

    # DROP any sections with supplementary materials
    n = doc.findall(".//body//sec[@sec-type='supplementary-material']")
    if len(n) > 0:
        for node in n:
            node.getparent().remove(node)

    # Add brackets to numbered references with superscript tags
    bib = doc.findall(".//sup//xref[@ref-type='bibr']")
    if len(bib) > 0:
        for node in bib:
            node.text = f" [{node.text}]"

    # Add ^ and _ to /sup and /sub tags
    sup = doc.findall(".//sup")
    if len(sup) > 0:
        for node in sup:
            node.text = f"^{node.text}"

    subs = doc.findall(".//sub")
    if len(subs) > 0:
        for node in subs:
            node.text = f"_{node.text}"

    return doc 